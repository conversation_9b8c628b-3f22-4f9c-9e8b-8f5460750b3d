import React, { useState, useEffect } from 'react';
import { Building, MapPin } from 'lucide-react';
import api from '../services/api';
import { companyApi } from '../api/companyApi';
import { plazaApi } from '../api/plazaApi';

/**
 * EntityFilter Component
 * 
 * A component for filtering dashboard data by company and plaza
 * based on user role and permissions
 * 
 * @param {Object} props - Component props
 * @param {string} props.userRole - Current user role
 * @param {Object} props.selectedEntity - Currently selected entity filters
 * @param {Function} props.onChange - Function to call when selection changes
 */
export function EntityFilter({ userRole = '', selectedEntity, onChange }) {
  // Ensure selectedEntity is always an object
  const entity = selectedEntity || {};
  const [companies, setCompanies] = useState([]);
  const [plazas, setPlazas] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Normalize userRole to prevent undefined issues
  const role = userRole || '';
  
  console.log('EntityFilter rendered with userRole:', role);

  // Fetch companies based on user role
  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        console.log('Fetching companies for user role:', role);
        setLoading(true);
        
        // Make sure we're only fetching for SuperAdmin
        if (role !== 'SuperAdmin') {
          console.log('User role is not SuperAdmin, skipping company fetch');
          setLoading(false);
          return;
        }
        
        // Use the companyApi service instead of direct API call
        console.log('Using companyApi.getCompanies() to fetch companies');
        const companiesData = await companyApi.getCompanies();
        
        console.log('Companies data received:', companiesData);
        setCompanies(companiesData || []);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching companies:', error);
        setError('Failed to load companies');
        setCompanies([]);
        setLoading(false);
      }
    };

    // Always call fetchCompanies - it has internal checks for user role
    fetchCompanies();
    
  }, [role]);

  // Fetch plazas when company selection changes
  useEffect(() => {
    const fetchPlazas = async () => {
      try {
        console.log('Fetching plazas. User role:', role, 'Selected company:', entity.companyId);
        setLoading(true);
        
        // For SuperAdmin: Only fetch plazas if a company is selected
        // For CompanyAdmin: Always fetch plazas (they're limited to their company)
        if (role === 'SuperAdmin' && !entity.companyId) {
          console.log('SuperAdmin with no company selected, skipping plaza fetch');
          setPlazas([]);
          setLoading(false);
          return;
        }
        
        let plazasData = [];
        
        if (entity.companyId) {
          // If company is selected, use the getPlazasByCompany method
          console.log('Using plazaApi.getPlazasByCompany() with companyId:', entity.companyId);
          try {
            // The plazaApi.getPlazasByCompany method now handles different response formats
            const plazasResponse = await plazaApi.getPlazasByCompany(entity.companyId);
            console.log('Plazas by company response:', plazasResponse);
            
            // Make sure we have an array
            if (Array.isArray(plazasResponse)) {
              plazasData = plazasResponse;
            } else {
              console.warn('Expected array from getPlazasByCompany but got:', typeof plazasResponse);
              plazasData = [];
            }
            
            console.log('Final plazas data:', plazasData);
          } catch (error) {
            console.error('Error in getPlazasByCompany:', error);
          }
        } else {
          // For CompanyAdmin without specific company selection
          try {
            const allPlazasResponse = await plazaApi.getAllPlazas();

            // Extract the data array from the response
            if (allPlazasResponse && allPlazasResponse.data && Array.isArray(allPlazasResponse.data)) {
              plazasData = allPlazasResponse.data;
            } else {
              plazasData = [];
            }
          } catch (error) {
            console.error('Error in getAllPlazas:', error);
          }
        }

        setPlazas(plazasData || []);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching plazas:', error);
        setError('Failed to load plazas');
        setPlazas([]);
        setLoading(false);
      }
    };

    fetchPlazas();
  }, [entity.companyId, role]);

  // Handle company selection change
  const handleCompanyChange = (e) => {
    const companyId = e.target.value === '' ? null : parseInt(e.target.value);
    
    // Reset plaza selection when company changes
    onChange({ 
      ...entity, 
      companyId,
      plazaId: null 
    });
  };

  // Handle plaza selection change
  const handlePlazaChange = (e) => {
    const plazaId = e.target.value === '' ? null : parseInt(e.target.value);
    onChange({ 
      ...entity, 
      plazaId 
    });
  };

  if (loading) {
    return <div className="text-sm text-gray-500">Loading filters...</div>;
  }

  if (error) {
    return <div className="text-sm text-red-500">{error}</div>;
  }

  return (
    <div className="flex flex-wrap gap-3 items-center">
      {/* Debug plazas - hidden */}
      <div style={{ display: 'none' }}>
        Plazas array length: {plazas?.length || 0}
        Plazas data: {JSON.stringify(plazas)}
      </div>
      
      {/* Company Filter - Only for SuperAdmin */}
      {role === 'SuperAdmin' && (
        <div>
          <select
            value={entity.companyId || ''}
            onChange={handleCompanyChange}
            className="px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={loading}
          >
            <option value="">All Companies</option>
            {companies.map((company) => (
              <option key={company.Id} value={company.Id}>
                {company.CompanyName}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Plaza Filter - For SuperAdmin and CompanyAdmin */}
      {(role === 'SuperAdmin' || role === 'CompanyAdmin') && (
        <div>
          <select
            value={entity.plazaId || ''}
            onChange={handlePlazaChange}
            className="px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={loading || (role === 'SuperAdmin' && !entity.companyId) || plazas.length === 0}
          >
            <option value="">All Plazas</option>
            {plazas && plazas.length > 0 ? (
              plazas.map((plaza) => (
                <option key={plaza.Id} value={plaza.Id}>
                  {plaza.PlazaName || plaza.Name || `Plaza ${plaza.Id}`}
                </option>
              ))
            ) : (
              <option disabled>No plazas available</option>
            )}
          </select>
        </div>
      )}
    </div>
  );
}