import api from '../services/api'; // Shared Axios instance

export const laneDigitalPayConfigApi = {
  /**
   * Fetches the list of all lane digital pay configurations.
   * GET /lane-digital-pay-configs
   */
  getAllConfigurations: async (timestamp) => {
    // Use provided timestamp or generate a new one to prevent caching
    const cacheBuster = timestamp || new Date().getTime();
    const response = await api.get(`/lane-digital-pay-configs?_=${cacheBuster}`);

      // Force all boolean fields to '1'/'0' format (workaround for backend issue)
      const fixedConfigurations = response.data.configurations.map(config => {
        // Helper function to normalize boolean values
        const normalizeToDigital = (value) => {
          // Special handling for string values with spaces (SQL Server char fields can have trailing spaces)
          if (typeof value === 'string') {
            const trimmedValue = value.trim();
            if (trimmedValue === 'Y') return '1';
            if (trimmedValue === 'N') return '0';
          }

          // Handle various true values
          if (value === 'Y' || value === 1 || value === true || value === '1' || value === 'true' || value === 'yes') {
            return '1';
          }

          // Handle various false values
          if (value === 'N' || value === 0 || value === false || value === '0' || value === 'false' || value === 'no' || value === null || value === undefined) {
            return '0';
          }

          return value === true ? '1' : '0';
        };

        // Create a new object with normalized boolean values
        const fixedConfig = {
          ...config,
          EnableCardPayment: normalizeToDigital(config.EnableCardPayment),
          EnableUPIPhonePe: normalizeToDigital(config.EnableUPIPhonePe),
          EnableSendSMS: normalizeToDigital(config.EnableSendSMS),
          ActiveStatus: normalizeToDigital(config.ActiveStatus),
          AllowBlacklistedVehicle: normalizeToDigital(config.AllowBlacklistedVehicle)
        };

        // Values normalized for consistency

        return fixedConfig;
      });

      return fixedConfigurations;
    }

    return response.data.configurations;
  },

  /**
   * Fetches the details of a single lane digital pay configuration by its ID.
   * GET /lane-digital-pay-configs/:id
   */
  getConfigurationById: async (id) => {
    const response = await api.get(`/lane-digital-pay-configs/${id}`);
    return response.data.configuration;
  },

  /**
   * Fetches lane digital pay configurations by lane ID.
   * GET /lane-digital-pay-configs/lane/:laneId
   */
  getConfigurationsByLane: async (laneId) => {
    const response = await api.get(`/lane-digital-pay-configs/lane/${laneId}`);
    return response.data.configurations;
  },

  /**
   * Fetches lane digital pay configurations by plaza ID.
   * GET /lane-digital-pay-configs/plaza/:plazaId
   */
  getConfigurationsByPlaza: async (plazaId) => {
    const response = await api.get(`/lane-digital-pay-configs/plaza/${plazaId}`);
    return response.data.configurations;
  },

  /**
   * Creates a new lane digital pay configuration.
   * POST /lane-digital-pay-configs
   */
  createConfiguration: async (data) => {
    // Helper function to normalize boolean values
    const normalizeBoolean = (value) => {
      // If value is already 'Y' or 'N', use it directly
      if (value === 'Y' || value === 'N') {
        return value;
      }

      // If value is already '1' or '0', convert to 'Y' or 'N'
      if (value === '1') {
        return 'Y';
      }

      if (value === '0') {
        return 'N';
      }

      // Convert other formats to 'Y' or 'N'
      return value === true || value === 'true' || value === 'y' || value === 1 ? 'Y' : 'N';
    };

    // Ensure boolean fields are properly formatted
    const formattedData = {
      ...data,
      EnableCardPayment: normalizeBoolean(data.EnableCardPayment),
      EnableUPIPhonePe: normalizeBoolean(data.EnableUPIPhonePe),
      EnableSendSMS: normalizeBoolean(data.EnableSendSMS),
      ActiveStatus: normalizeBoolean(data.ActiveStatus),
      AllowBlacklistedVehicle: normalizeBoolean(data.AllowBlacklistedVehicle)
    };

    // Boolean values formatted for database

    const response = await api.post('/lane-digital-pay-configs', formattedData);
    return response.data;
  },

  /**
   * Updates a lane digital pay configuration by ID.
   * PUT /lane-digital-pay-configs/:id
   */
  updateConfiguration: async (id, data) => {
    // Helper function to normalize boolean values
    const normalizeBoolean = (value) => {
      // If value is already 'Y' or 'N', use it directly
      if (value === 'Y' || value === 'N') {
        return value;
      }

      // If value is already '1' or '0', convert to 'Y' or 'N'
      if (value === '1') {
        return 'Y';
      }

      if (value === '0') {
        return 'N';
      }

      // Convert other formats to 'Y' or 'N'
      return value === true || value === 'true' || value === 'y' || value === 1 ? 'Y' : 'N';
    };

    // Ensure boolean fields are properly formatted
    const formattedData = {
      ...data,
      EnableCardPayment: normalizeBoolean(data.EnableCardPayment),
      EnableUPIPhonePe: normalizeBoolean(data.EnableUPIPhonePe),
      EnableSendSMS: normalizeBoolean(data.EnableSendSMS),
      ActiveStatus: normalizeBoolean(data.ActiveStatus),
      AllowBlacklistedVehicle: normalizeBoolean(data.AllowBlacklistedVehicle)
    };

    // Boolean values formatted for database

    const response = await api.put(`/lane-digital-pay-configs/${id}`, formattedData);
    return response.data;
  },

  /**
   * Deletes a lane digital pay configuration by ID.
   * DELETE /lane-digital-pay-configs/:id
   */
  deleteConfiguration: async (id) => {
    try {
      const response = await api.delete(`/lane-digital-pay-configs/${id}`);

      // Add a timestamp to the response to ensure the UI knows this is a fresh response
      const responseWithTimestamp = {
        ...response.data,
        timestamp: new Date().getTime()
      };

      return responseWithTimestamp;
    } catch (error) {
      console.error('Digital Pay API - Delete error:', error.message);
      throw error;
    }
  },

  /**
   * Toggles the active status of a lane digital pay configuration.
   * PATCH /lane-digital-pay-configs/:id/toggle-status
   */
  toggleConfigurationStatus: async (id, updatedBy) => {
    try {
      // Make sure we have valid parameters
      if (!id) {
        throw new Error('Configuration ID is required');
      }

      if (!updatedBy) {
        updatedBy = 'admin'; // Default value if not provided
      }

      // Add a cache-busting parameter to ensure we're not getting cached responses
      const cacheBuster = new Date().getTime();

      // Use a direct URL with the ID as a number to avoid any string conversion issues
      const configId = typeof id === 'string' ? parseInt(id, 10) : id;

      if (isNaN(configId)) {
        throw new Error('Invalid configuration ID format');
      }

      // Use axios instead of fetch for consistency with other API calls
      const response = await api.patch(`/lane-digital-pay-configs/${configId}/toggle-status?_=${cacheBuster}`, {
        UpdatedBy: updatedBy
      });

      // Ensure the response contains the expected fields
      if (!response.data || !response.data.success) {
        throw new Error('API returned unsuccessful response');
      }

      // Ensure newStatus is properly formatted as a string
      if (response.data.newStatus !== undefined) {
        response.data.newStatus = String(response.data.newStatus);
      }

      return response.data;
    } catch (error) {
      console.error('Failed to toggle configuration status:', error);
      throw error;
    }
  }
};