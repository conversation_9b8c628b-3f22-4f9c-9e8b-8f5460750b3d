const db = require('./src/config/database');

/**
 * Check which PlazaCodes have transaction data
 */

async function checkTransactionData() {
  try {
    console.log('=== CHECKING TRANSACTION DATA BY PLAZA ===\n');

    // Get all PlazaCodes with transaction data
    const transactionDataQuery = `
      SELECT 
        t.PlazaCode,
        t.PlazaName,
        COUNT(*) as TransactionCount,
        SUM(t.ParkingFee + t.iTotalGSTFee) as TotalRevenue,
        MIN(t.ExitDateTime) as EarliestTransaction,
        MAX(t.ExitDateTime) as LatestTransaction
      FROM tblParkwiz_Parking_Data t
      WHERE t.ExitDateTime IS NOT NULL
      AND t.ExitDateTime >= DATEADD(day, -30, GETDATE())
      GROUP BY t.PlazaCode, t.PlazaName
      ORDER BY TransactionCount DESC
    `;
    
    const result = await db.query(transactionDataQuery);
    
    console.log(`Found transaction data for ${result.recordset.length} plazas (last 30 days):`);
    console.log('');
    
    result.recordset.forEach((plaza, index) => {
      console.log(`${index + 1}. PlazaCode: ${plaza.PlazaCode} - ${plaza.PlazaName}`);
      console.log(`   Transactions: ${plaza.TransactionCount}`);
      console.log(`   Revenue: ₹${plaza.TotalRevenue?.toFixed(2) || 0}`);
      console.log(`   Date Range: ${plaza.EarliestTransaction} to ${plaza.LatestTransaction}`);
      console.log('');
    });

    // Check if PlazaCode 102 (assigned to accr) has any historical data
    console.log('\nCHECKING HISTORICAL DATA FOR PLAZACODE 102:');
    console.log('===========================================');
    
    const historicalQuery = `
      SELECT 
        COUNT(*) as TransactionCount,
        MIN(t.ExitDateTime) as EarliestTransaction,
        MAX(t.ExitDateTime) as LatestTransaction
      FROM tblParkwiz_Parking_Data t
      WHERE t.PlazaCode = '102'
      AND t.ExitDateTime IS NOT NULL
    `;
    
    const historicalResult = await db.query(historicalQuery);
    
    if (historicalResult.recordset.length > 0) {
      const data = historicalResult.recordset[0];
      console.log(`Historical data for PlazaCode 102:`);
      console.log(`  • Total Transactions: ${data.TransactionCount}`);
      if (data.TransactionCount > 0) {
        console.log(`  • Date Range: ${data.EarliestTransaction} to ${data.LatestTransaction}`);
      } else {
        console.log(`  • No transaction data found for PlazaCode 102`);
      }
    }

    console.log('\n=== CHECK COMPLETE ===');
    process.exit(0);
    
  } catch (error) {
    console.error('Error during check:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

checkTransactionData();
